/**
 * Database utilities for testing
 * <PERSON><PERSON><PERSON> ti<PERSON> ích database cho testing
 */

import { PrismaClient } from '@prisma/client'
import { mockUsers, mockCategories, mockProducts, mockOrders, mockOrderItems, mockCarts, mockCartItems, mockAddresses, mockReviews } from '../fixtures/mock-data'

// Test database instance
let prisma: PrismaClient

// Initialize test database
export const initTestDatabase = async () => {
  if (!prisma) {
    prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_URL || 'postgresql://test:test@localhost:5432/ns_shop_test',
        },
      },
    })
  }
  
  // Connect to database
  await prisma.$connect()
  return prisma
}

// Clean up test database
export const cleanupTestDatabase = async () => {
  if (prisma) {
    // Delete all data in reverse order of dependencies
    await prisma.review.deleteMany()
    await prisma.orderItem.deleteMany()
    await prisma.order.deleteMany()
    await prisma.cartItem.deleteMany()
    await prisma.cart.deleteMany()
    await prisma.address.deleteMany()
    await prisma.product.deleteMany()
    await prisma.category.deleteMany()
    await prisma.user.deleteMany()
    
    await prisma.$disconnect()
  }
}

// Seed test database with mock data
export const seedTestDatabase = async () => {
  const db = await initTestDatabase()
  
  // Create users
  for (const user of mockUsers) {
    await db.user.create({
      data: user,
    })
  }
  
  // Create categories
  for (const category of mockCategories) {
    await db.category.create({
      data: category,
    })
  }
  
  // Create products
  for (const product of mockProducts) {
    await db.product.create({
      data: product,
    })
  }
  
  // Create carts
  for (const cart of mockCarts) {
    await db.cart.create({
      data: cart,
    })
  }
  
  // Create cart items
  for (const cartItem of mockCartItems) {
    await db.cartItem.create({
      data: cartItem,
    })
  }
  
  // Create addresses
  for (const address of mockAddresses) {
    await db.address.create({
      data: address,
    })
  }
  
  // Create orders
  for (const order of mockOrders) {
    await db.order.create({
      data: order,
    })
  }
  
  // Create order items
  for (const orderItem of mockOrderItems) {
    await db.orderItem.create({
      data: orderItem,
    })
  }
  
  // Create reviews
  for (const review of mockReviews) {
    await db.review.create({
      data: review,
    })
  }
  
  return db
}

// Get test database instance
export const getTestDatabase = () => {
  if (!prisma) {
    throw new Error('Test database not initialized. Call initTestDatabase() first.')
  }
  return prisma
}

// Database test utilities
export const createTestUser = async (userData = {}) => {
  const db = getTestDatabase()
  return await db.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Test User',
      password: 'hashedpassword',
      role: 'USER',
      ...userData,
    },
  })
}

export const createTestProduct = async (productData = {}) => {
  const db = getTestDatabase()
  
  // Ensure we have a category
  let category = await db.category.findFirst()
  if (!category) {
    category = await db.category.create({
      data: {
        name: 'Test Category',
        description: 'Test category description',
        slug: 'test-category',
        image: 'https://example.com/category.jpg',
      },
    })
  }
  
  return await db.product.create({
    data: {
      name: 'Test Product',
      description: 'Test product description',
      price: 99.99,
      images: ['https://example.com/product.jpg'],
      categoryId: category.id,
      stock: 10,
      sku: `TEST-SKU-${Date.now()}`,
      slug: `test-product-${Date.now()}`,
      status: 'ACTIVE',
      tags: ['test'],
      ...productData,
    },
  })
}

export const createTestOrder = async (orderData = {}) => {
  const db = getTestDatabase()
  
  // Ensure we have a user
  let user = await db.user.findFirst()
  if (!user) {
    user = await createTestUser()
  }
  
  return await db.order.create({
    data: {
      userId: user.id,
      total: 99.99,
      status: 'PENDING',
      paymentMethod: 'COD',
      paymentStatus: 'PENDING',
      shippingAddress: {
        fullName: 'Test User',
        phone: '0123456789',
        address: '123 Test Street',
        ward: 'Test Ward',
        district: 'Test District',
        province: 'Test Province',
      },
      ...orderData,
    },
  })
}

// Transaction helpers for testing
export const withTransaction = async (callback: (tx: PrismaClient) => Promise<void>) => {
  const db = getTestDatabase()
  return await db.$transaction(async (tx) => {
    await callback(tx as PrismaClient)
  })
}

// Reset database to clean state
export const resetTestDatabase = async () => {
  await cleanupTestDatabase()
  await seedTestDatabase()
}

// Mock Prisma client for unit tests
export const createMockPrismaClient = () => {
  return {
    user: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
    },
    product: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
    },
    category: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
    },
    order: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
    },
    cart: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
    },
    $connect: jest.fn(),
    $disconnect: jest.fn(),
    $transaction: jest.fn(),
  }
}
